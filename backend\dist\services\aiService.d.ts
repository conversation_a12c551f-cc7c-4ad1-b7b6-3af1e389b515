export interface Character {
    name: string;
    aliases: string[];
    description: string;
    personality: string[];
    background: string;
    firstAppearance: number;
    importanceScore: number;
    characterArc: string;
}
export interface PlotLine {
    name: string;
    type: 'MAIN' | 'ROMANCE' | 'SIDE' | 'BACKGROUND';
    description: string;
    chapters: number[];
    keyEvents: any;
    status: 'ONGOING' | 'RESOLVED' | 'ABANDONED';
}
export interface NovelAnalysis {
    worldBuilding: any;
    themes: string[];
    writingStyle: any;
    characters: Character[];
    plotLines: PlotLine[];
}
export declare class AIService {
    private model;
    constructor();
    testConnection(): Promise<{
        success: boolean;
        response: any;
        timestamp: string;
    }>;
    analyzeText(text: string, type?: string): Promise<any>;
    analyzeNovelStructure(text: string): Promise<NovelAnalysis>;
    rewriteText(text: string, rules: any[], context?: any): Promise<{
        originalText: string;
        rewrittenText: any;
        rules: any[];
        timestamp: string;
    }>;
    extractCharacters(text: string): Promise<any>;
    analyzePlot(text: string): Promise<any>;
    generateSummary(text: string, length?: string): Promise<{
        summary: any;
        originalLength: number;
        summaryLength: any;
        compressionRatio: string;
    }>;
    private getAnalysisPrompt;
    private getRewritePrompt;
}
//# sourceMappingURL=aiService.d.ts.map