"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.createError = exports.errorHandler = void 0;
const errorHandler = (err, req, res, next) => {
    const statusCode = err.statusCode || 500;
    const message = err.message || 'Internal Server Error';
    // 记录错误
    console.error(`Error ${statusCode}: ${message}`);
    console.error(err.stack);
    // 开发环境返回详细错误信息
    if (process.env.NODE_ENV === 'development') {
        res.status(statusCode).json({
            error: {
                message,
                statusCode,
                stack: err.stack,
                timestamp: new Date().toISOString(),
                path: req.path,
                method: req.method
            }
        });
    }
    else {
        // 生产环境只返回基本错误信息
        res.status(statusCode).json({
            error: {
                message: statusCode === 500 ? 'Internal Server Error' : message,
                statusCode,
                timestamp: new Date().toISOString()
            }
        });
    }
};
exports.errorHandler = errorHandler;
const createError = (message, statusCode = 500) => {
    const error = new Error(message);
    error.statusCode = statusCode;
    error.isOperational = true;
    return error;
};
exports.createError = createError;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map