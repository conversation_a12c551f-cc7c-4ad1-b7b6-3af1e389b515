"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const gemini_1 = require("../config/gemini");
const errorHandler_1 = require("../middleware/errorHandler");
class AIService {
    constructor() {
        this.model = (0, gemini_1.getGeminiModel)();
    }
    async testConnection() {
        try {
            const result = await this.model.generateContent({
                contents: [{
                        role: 'user',
                        parts: [{ text: '你好，请回复"连接成功"' }]
                    }],
                generationConfig: {
                    temperature: 0.1,
                    maxOutputTokens: 100
                }
            });
            return {
                success: true,
                response: result.response.text(),
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`AI connection failed: ${error.message}`, 500);
        }
    }
    async analyzeText(text, type = 'general') {
        try {
            const prompt = this.getAnalysisPrompt(text, type);
            const result = await this.model.generateContent({
                contents: [{
                        role: 'user',
                        parts: [{ text: prompt }]
                    }],
                generationConfig: gemini_1.analysisConfig
            });
            const response = result.response.text();
            try {
                return JSON.parse(response);
            }
            catch {
                return { analysis: response };
            }
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Text analysis failed: ${error.message}`, 500);
        }
    }
    async analyzeNovelStructure(text) {
        try {
            const prompt = `
请分析以下小说文本，提取结构化信息。请以JSON格式返回分析结果：

{
  "worldBuilding": {
    "setting": "故事背景设定",
    "timeperiod": "时代背景",
    "locations": ["主要地点1", "主要地点2"],
    "magicSystem": "力量体系描述",
    "socialStructure": "社会结构描述"
  },
  "themes": ["主题1", "主题2", "主题3"],
  "writingStyle": {
    "tone": "整体语调",
    "pace": "节奏快慢",
    "perspective": "叙述视角",
    "language": "语言风格描述"
  },
  "characters": [
    {
      "name": "角色名",
      "aliases": ["别名1", "别名2"],
      "description": "外貌描述",
      "personality": ["性格特征1", "性格特征2"],
      "background": "背景故事",
      "firstAppearance": 1,
      "importanceScore": 95,
      "characterArc": "角色发展弧线"
    }
  ],
  "plotLines": [
    {
      "name": "情节线名称",
      "type": "MAIN",
      "description": "情节线描述",
      "chapters": [1, 2, 3],
      "keyEvents": {"event1": "描述", "event2": "描述"},
      "status": "ONGOING"
    }
  ]
}

小说文本：
${text.substring(0, 20000)}...

请仔细分析文本内容，提取准确的角色信息、情节线和世界观设定。`;
            const result = await this.model.generateContent({
                contents: [{
                        role: 'user',
                        parts: [{ text: prompt }]
                    }],
                generationConfig: {
                    ...gemini_1.analysisConfig,
                    maxOutputTokens: 4096
                }
            });
            const response = result.response.text();
            try {
                // 清理响应文本，移除可能的markdown标记
                const cleanResponse = response.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
                return JSON.parse(cleanResponse);
            }
            catch (parseError) {
                console.error('Failed to parse AI response:', response);
                throw (0, errorHandler_1.createError)('Failed to parse AI analysis result', 500);
            }
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Novel structure analysis failed: ${error.message}`, 500);
        }
    }
    async rewriteText(text, rules, context) {
        try {
            const prompt = this.getRewritePrompt(text, rules, context);
            const result = await this.model.generateContent({
                contents: [{
                        role: 'user',
                        parts: [{ text: prompt }]
                    }],
                generationConfig: gemini_1.textGenerationConfig
            });
            return {
                originalText: text,
                rewrittenText: result.response.text(),
                rules: rules,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Text rewriting failed: ${error.message}`, 500);
        }
    }
    async extractCharacters(text) {
        try {
            const prompt = `
请从以下文本中提取所有角色信息，以JSON格式返回：

{
  "characters": [
    {
      "name": "角色名",
      "aliases": ["别名1", "别名2"],
      "description": "外貌描述",
      "personality": ["性格特征1", "性格特征2"],
      "relationships": ["与其他角色的关系"],
      "importance": 85
    }
  ]
}

文本：
${text.substring(0, 10000)}`;
            const result = await this.model.generateContent({
                contents: [{
                        role: 'user',
                        parts: [{ text: prompt }]
                    }],
                generationConfig: gemini_1.analysisConfig
            });
            const response = result.response.text();
            const cleanResponse = response.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            return JSON.parse(cleanResponse);
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Character extraction failed: ${error.message}`, 500);
        }
    }
    async analyzePlot(text) {
        try {
            const prompt = `
请分析以下文本的情节结构，以JSON格式返回：

{
  "plotLines": [
    {
      "name": "情节线名称",
      "type": "主线/感情线/支线",
      "description": "情节描述",
      "keyEvents": ["关键事件1", "关键事件2"],
      "conflicts": ["冲突点1", "冲突点2"],
      "resolution": "解决方案或状态"
    }
  ],
  "themes": ["主题1", "主题2"],
  "pacing": "节奏分析",
  "climax": "高潮部分描述"
}

文本：
${text.substring(0, 10000)}`;
            const result = await this.model.generateContent({
                contents: [{
                        role: 'user',
                        parts: [{ text: prompt }]
                    }],
                generationConfig: gemini_1.analysisConfig
            });
            const response = result.response.text();
            const cleanResponse = response.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
            return JSON.parse(cleanResponse);
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Plot analysis failed: ${error.message}`, 500);
        }
    }
    async generateSummary(text, length = 'medium') {
        try {
            const lengthMap = {
                short: '100字以内',
                medium: '200-300字',
                long: '500字以内'
            };
            const prompt = `
请为以下文本生成${lengthMap[length] || '200-300字'}的摘要，保留主要情节和关键信息：

文本：
${text.substring(0, 15000)}`;
            const result = await this.model.generateContent({
                contents: [{
                        role: 'user',
                        parts: [{ text: prompt }]
                    }],
                generationConfig: {
                    ...gemini_1.analysisConfig,
                    maxOutputTokens: 1024
                }
            });
            return {
                summary: result.response.text(),
                originalLength: text.length,
                summaryLength: result.response.text().length,
                compressionRatio: (result.response.text().length / text.length * 100).toFixed(2) + '%'
            };
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Summary generation failed: ${error.message}`, 500);
        }
    }
    getAnalysisPrompt(text, type) {
        const prompts = {
            general: `请分析以下文本的内容、风格和特点：\n\n${text}`,
            character: `请分析以下文本中的角色特征、性格和关系：\n\n${text}`,
            plot: `请分析以下文本的情节结构、冲突和发展：\n\n${text}`,
            style: `请分析以下文本的写作风格、语言特色和表达方式：\n\n${text}`
        };
        return prompts[type] || prompts.general;
    }
    getRewritePrompt(text, rules, context) {
        let prompt = `请根据以下规则重写文本，保持故事的连贯性和角色的一致性：\n\n`;
        // 添加修改规则
        if (rules && rules.length > 0) {
            prompt += `修改规则：\n`;
            rules.forEach((rule, index) => {
                prompt += `${index + 1}. ${rule.name}: ${rule.description}\n`;
                if (rule.config) {
                    prompt += `   配置: ${JSON.stringify(rule.config)}\n`;
                }
            });
            prompt += `\n`;
        }
        // 添加上下文信息
        if (context) {
            prompt += `上下文信息：\n`;
            if (context.previousChapter) {
                prompt += `前一章摘要: ${context.previousChapter}\n`;
            }
            if (context.characters) {
                prompt += `主要角色: ${context.characters.join(', ')}\n`;
            }
            if (context.plotLines) {
                prompt += `当前情节线: ${context.plotLines.join(', ')}\n`;
            }
            prompt += `\n`;
        }
        prompt += `原文：\n${text}\n\n请重写上述文本：`;
        return prompt;
    }
}
exports.AIService = AIService;
//# sourceMappingURL=aiService.js.map