const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config();

// 测试 Gemini API 连接
async function testGeminiConnection() {
  try {
    console.log('🔍 开始测试 Gemini API 连接...');

    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY 环境变量未设置');
    }

    console.log('🔑 API Key 已配置');

    // 初始化 Gemini AI
    const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    console.log('🤖 正在测试基础连接...');

    // 测试基础连接
    const result = await model.generateContent({
      contents: [{
        role: 'user',
        parts: [{ text: '你好，请回复"连接成功"' }]
      }],
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 100
      }
    });

    const response = result.response.text();
    console.log('📝 AI 响应:', response);

    if (response.includes('连接成功') || response.includes('成功') || response.includes('你好')) {
      console.log('✅ 基础连接测试成功!');
    } else {
      console.log('⚠️ 响应内容异常，但连接正常');
    }

    // 测试文本分析功能
    console.log('\n🔍 测试文本分析功能...');

    const analysisPrompt = `
请分析以下小说片段，提取角色信息和情节要点：

"宁长久低着头，一丝不苟地听着，待到老人问话，他恭敬点头："记住了。"
少女同样言语恭敬，她低着头，眸子微动，隐有不屑与怨怒。
宁擒水点了点头，道："那你们便好生打坐静心，待到子时，随师父一同降魔。""

请以JSON格式返回分析结果：
{
  "characters": ["角色名1", "角色名2"],
  "plot_summary": "情节摘要",
  "tone": "语调描述"
}`;

    const analysisResult = await model.generateContent({
      contents: [{
        role: 'user',
        parts: [{ text: analysisPrompt }]
      }],
      generationConfig: {
        temperature: 0.3,
        maxOutputTokens: 500
      }
    });

    const analysisResponse = analysisResult.response.text();
    console.log('📊 分析结果:', analysisResponse);

    // 尝试解析JSON
    try {
      const cleanResponse = analysisResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      const parsed = JSON.parse(cleanResponse);
      console.log('✅ JSON 解析成功:', parsed);
    } catch (parseError) {
      console.log('⚠️ JSON 解析失败，但分析功能正常');
    }

    // 测试重写功能
    console.log('\n✏️ 测试文本重写功能...');

    const rewritePrompt = `
请将以下文本重写，增加更多感情描述：

原文："宁长久恭敬点头说记住了。"

重写要求：
1. 保持原意不变
2. 增加心理描写
3. 丰富动作描述
4. 字数增加到原文的2倍左右

请直接返回重写后的文本：`;

    const rewriteResult = await model.generateContent({
      contents: [{
        role: 'user',
        parts: [{ text: rewritePrompt }]
      }],
      generationConfig: {
        temperature: 0.7,
        maxOutputTokens: 300
      }
    });

    const rewriteResponse = rewriteResult.response.text();
    console.log('✏️ 重写结果:', rewriteResponse);

    console.log('\n🎉 Gemini API 测试完成!');

    return {
      success: true,
      basicConnection: true,
      analysisFunction: true,
      rewriteFunction: true
    };

  } catch (error) {
    console.error('❌ 测试失败:', error.message);

    if (error.message.includes('API_KEY')) {
      console.log('💡 请检查 .env 文件中的 GEMINI_API_KEY 配置');
    } else if (error.message.includes('quota') || error.message.includes('limit')) {
      console.log('💡 API 配额不足或达到速率限制');
    } else if (error.message.includes('network') || error.message.includes('fetch')) {
      console.log('💡 网络连接问题，请检查网络设置');
    }

    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
if (require.main === module) {
  testGeminiConnection().then(result => {
    if (result.success) {
      console.log('\n🎉 所有测试通过!');
      console.log('✅ Gemini API 已准备就绪，可以开始使用小说重塑功能');
    } else {
      console.log('\n💥 测试失败!');
      console.log('❌ 请解决上述问题后重试');
    }
  });
}

module.exports = { testGeminiConnection };
