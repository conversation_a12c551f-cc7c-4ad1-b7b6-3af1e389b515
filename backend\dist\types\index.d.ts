export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface Pagination {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
}
export interface PaginatedResponse<T> extends ApiResponse<T> {
    pagination: Pagination;
}
export interface ProjectCreateInput {
    name: string;
    description?: string;
    settings?: Record<string, any>;
}
export interface ProjectUpdateInput {
    name?: string;
    description?: string;
    settings?: Record<string, any>;
}
export interface NovelImportInput {
    projectId: string;
    title: string;
    author?: string;
    file: Express.Multer.File;
}
export interface ChapterData {
    title: string;
    content: string;
    number: number;
}
export interface ModificationRule {
    id: string;
    name: string;
    type: 'CONTENT_WEIGHT' | 'CHARACTER_ADJUSTMENT' | 'STYLE_CHANGE' | 'PLOT_FILTER';
    description?: string;
    config: ModificationRuleConfig;
    isActive: boolean;
}
export interface ModificationRuleConfig {
    romance_weight?: number;
    action_weight?: number;
    dialogue_weight?: number;
    description_weight?: number;
    character_adjustments?: {
        character_name: string;
        original_traits: string[];
        target_traits: string[];
        adjustment_strength: number;
    }[];
    writing_style?: {
        tone: 'formal' | 'casual' | 'humorous' | 'serious' | 'romantic' | 'dramatic';
        pace: 'fast' | 'medium' | 'slow';
        detail_level: 'brief' | 'moderate' | 'detailed';
        vocabulary_level: 'simple' | 'moderate' | 'complex';
    };
    plot_filters?: {
        remove_patterns: string[];
        enhance_patterns: string[];
        focus_themes: string[];
    };
    length_adjustment?: {
        target_ratio: number;
        min_length?: number;
        max_length?: number;
    };
}
export interface CharacterAnalysis {
    name: string;
    aliases: string[];
    description: string;
    personality: string[];
    background: string;
    firstAppearance: number;
    importanceScore: number;
    characterArc: string;
    relationships: RelationshipInfo[];
}
export interface RelationshipInfo {
    targetCharacter: string;
    type: 'ROMANTIC' | 'FAMILY' | 'FRIEND' | 'ENEMY' | 'MENTOR' | 'COLLEAGUE' | 'OTHER';
    description: string;
    development: string[];
}
export interface PlotLineAnalysis {
    name: string;
    type: 'MAIN' | 'ROMANCE' | 'SIDE' | 'BACKGROUND';
    description: string;
    chapters: number[];
    keyEvents: PlotEvent[];
    status: 'ONGOING' | 'RESOLVED' | 'ABANDONED';
    conflicts: string[];
    themes: string[];
}
export interface PlotEvent {
    chapter: number;
    description: string;
    importance: number;
    characters: string[];
    type: 'conflict' | 'resolution' | 'development' | 'climax' | 'setup';
}
export interface ChapterAnalysis {
    summary: string;
    characters: string[];
    plotLines: string[];
    contentType: {
        romance_ratio: number;
        action_ratio: number;
        dialogue_ratio: number;
        description_ratio: number;
    };
    emotionalTone: string;
    pacing: 'slow' | 'medium' | 'fast';
    importanceScore: number;
    themes: string[];
    keyEvents: string[];
    conflicts: string[];
    resolutions: string[];
}
export interface NovelStructureAnalysis {
    worldBuilding: {
        setting: string;
        timeperiod: string;
        locations: string[];
        magicSystem?: string;
        socialStructure?: string;
        rules: string[];
    };
    themes: string[];
    writingStyle: {
        tone: string;
        pace: string;
        perspective: string;
        language: string;
        vocabulary_level: string;
    };
    characters: CharacterAnalysis[];
    plotLines: PlotLineAnalysis[];
    overallStructure: {
        acts: ActStructure[];
        climax_chapter: number;
        resolution_chapter: number;
        pacing_analysis: string;
    };
}
export interface ActStructure {
    name: string;
    start_chapter: number;
    end_chapter: number;
    description: string;
    key_events: string[];
}
export interface ContextInjection {
    global_context: {
        novel_summary: string;
        main_characters: CharacterAnalysis[];
        current_plot_status: PlotLineAnalysis[];
        world_building_rules: string[];
        writing_style: any;
    };
    local_context: {
        previous_chapters_summary: string;
        current_chapter_analysis: ChapterAnalysis;
        next_chapter_preview?: string;
        related_plot_lines: PlotLineAnalysis[];
        active_characters: CharacterAnalysis[];
    };
    modification_instructions: {
        rules: ModificationRule[];
        target_length_ratio: number;
        style_requirements: string[];
        content_focus: string[];
        preserve_elements: string[];
    };
}
export interface ConsistencyCheck {
    character_consistency: {
        personality_drift: number;
        behavior_consistency: number;
        dialogue_style_consistency: number;
        relationship_consistency: number;
    };
    plot_consistency: {
        timeline_consistency: boolean;
        causality_consistency: boolean;
        world_building_consistency: boolean;
        theme_consistency: number;
    };
    style_consistency: {
        tone_consistency: number;
        vocabulary_consistency: number;
        sentence_structure_consistency: number;
        pacing_consistency: number;
    };
    overall_score: number;
    issues: ConsistencyIssue[];
    suggestions: string[];
}
export interface ConsistencyIssue {
    type: 'character' | 'plot' | 'style' | 'world_building';
    severity: 'low' | 'medium' | 'high';
    description: string;
    location: {
        chapter: number;
        paragraph?: number;
    };
    suggestion: string;
}
export interface ProcessingTask {
    id: string;
    type: 'NOVEL_ANALYSIS' | 'CHAPTER_ANALYSIS' | 'CHAPTER_MODIFY' | 'CONSISTENCY_CHECK';
    status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    progress: number;
    input?: any;
    output?: any;
    error?: string;
    novelId?: string;
    chapterId?: string;
    createdAt: Date;
    updatedAt: Date;
    completedAt?: Date;
}
export interface FileUploadResult {
    filename: string;
    originalName: string;
    size: number;
    mimetype: string;
    path: string;
}
export interface AppError extends Error {
    statusCode?: number;
    isOperational?: boolean;
}
//# sourceMappingURL=index.d.ts.map