// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 分页类型
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  pagination: Pagination;
}

// 项目相关类型
export interface ProjectCreateInput {
  name: string;
  description?: string;
  settings?: Record<string, any>;
}

export interface ProjectUpdateInput {
  name?: string;
  description?: string;
  settings?: Record<string, any>;
}

// 小说相关类型
export interface NovelImportInput {
  projectId: string;
  title: string;
  author?: string;
  file: Express.Multer.File;
}

export interface ChapterData {
  title: string;
  content: string;
  number: number;
}

// 修改规则类型
export interface ModificationRule {
  id: string;
  name: string;
  type: 'CONTENT_WEIGHT' | 'CHARACTER_ADJUSTMENT' | 'STYLE_CHANGE' | 'PLOT_FILTER';
  description?: string;
  config: ModificationRuleConfig;
  isActive: boolean;
}

export interface ModificationRuleConfig {
  // 内容权重调整
  romance_weight?: number;      // 感情戏权重 (0-100)
  action_weight?: number;       // 动作戏权重 (0-100)
  dialogue_weight?: number;     // 对话权重 (0-100)
  description_weight?: number;  // 描述权重 (0-100)
  
  // 角色性格修正
  character_adjustments?: {
    character_name: string;
    original_traits: string[];
    target_traits: string[];
    adjustment_strength: number; // 调整强度 (0-100)
  }[];
  
  // 文风调整
  writing_style?: {
    tone: 'formal' | 'casual' | 'humorous' | 'serious' | 'romantic' | 'dramatic';
    pace: 'fast' | 'medium' | 'slow';
    detail_level: 'brief' | 'moderate' | 'detailed';
    vocabulary_level: 'simple' | 'moderate' | 'complex';
  };
  
  // 情节过滤
  plot_filters?: {
    remove_patterns: string[];   // 要删除的情节模式
    enhance_patterns: string[];  // 要增强的情节模式
    focus_themes: string[];      // 重点主题
  };
  
  // 长度控制
  length_adjustment?: {
    target_ratio: number;        // 目标长度比例 (0.5-3.0)
    min_length?: number;         // 最小长度
    max_length?: number;         // 最大长度
  };
}

// AI 分析结果类型
export interface CharacterAnalysis {
  name: string;
  aliases: string[];
  description: string;
  personality: string[];
  background: string;
  firstAppearance: number;
  importanceScore: number;
  characterArc: string;
  relationships: RelationshipInfo[];
}

export interface RelationshipInfo {
  targetCharacter: string;
  type: 'ROMANTIC' | 'FAMILY' | 'FRIEND' | 'ENEMY' | 'MENTOR' | 'COLLEAGUE' | 'OTHER';
  description: string;
  development: string[];
}

export interface PlotLineAnalysis {
  name: string;
  type: 'MAIN' | 'ROMANCE' | 'SIDE' | 'BACKGROUND';
  description: string;
  chapters: number[];
  keyEvents: PlotEvent[];
  status: 'ONGOING' | 'RESOLVED' | 'ABANDONED';
  conflicts: string[];
  themes: string[];
}

export interface PlotEvent {
  chapter: number;
  description: string;
  importance: number;
  characters: string[];
  type: 'conflict' | 'resolution' | 'development' | 'climax' | 'setup';
}

export interface ChapterAnalysis {
  summary: string;
  characters: string[];
  plotLines: string[];
  contentType: {
    romance_ratio: number;
    action_ratio: number;
    dialogue_ratio: number;
    description_ratio: number;
  };
  emotionalTone: string;
  pacing: 'slow' | 'medium' | 'fast';
  importanceScore: number;
  themes: string[];
  keyEvents: string[];
  conflicts: string[];
  resolutions: string[];
}

export interface NovelStructureAnalysis {
  worldBuilding: {
    setting: string;
    timeperiod: string;
    locations: string[];
    magicSystem?: string;
    socialStructure?: string;
    rules: string[];
  };
  themes: string[];
  writingStyle: {
    tone: string;
    pace: string;
    perspective: string;
    language: string;
    vocabulary_level: string;
  };
  characters: CharacterAnalysis[];
  plotLines: PlotLineAnalysis[];
  overallStructure: {
    acts: ActStructure[];
    climax_chapter: number;
    resolution_chapter: number;
    pacing_analysis: string;
  };
}

export interface ActStructure {
  name: string;
  start_chapter: number;
  end_chapter: number;
  description: string;
  key_events: string[];
}

// 上下文注入类型
export interface ContextInjection {
  // 全局上下文
  global_context: {
    novel_summary: string;
    main_characters: CharacterAnalysis[];
    current_plot_status: PlotLineAnalysis[];
    world_building_rules: string[];
    writing_style: any;
  };
  
  // 局部上下文
  local_context: {
    previous_chapters_summary: string;
    current_chapter_analysis: ChapterAnalysis;
    next_chapter_preview?: string;
    related_plot_lines: PlotLineAnalysis[];
    active_characters: CharacterAnalysis[];
  };
  
  // 修改指令
  modification_instructions: {
    rules: ModificationRule[];
    target_length_ratio: number;
    style_requirements: string[];
    content_focus: string[];
    preserve_elements: string[];
  };
}

// 一致性校验类型
export interface ConsistencyCheck {
  // 角色一致性
  character_consistency: {
    personality_drift: number;
    behavior_consistency: number;
    dialogue_style_consistency: number;
    relationship_consistency: number;
  };
  
  // 情节一致性
  plot_consistency: {
    timeline_consistency: boolean;
    causality_consistency: boolean;
    world_building_consistency: boolean;
    theme_consistency: number;
  };
  
  // 文风一致性
  style_consistency: {
    tone_consistency: number;
    vocabulary_consistency: number;
    sentence_structure_consistency: number;
    pacing_consistency: number;
  };
  
  // 整体评分
  overall_score: number;
  issues: ConsistencyIssue[];
  suggestions: string[];
}

export interface ConsistencyIssue {
  type: 'character' | 'plot' | 'style' | 'world_building';
  severity: 'low' | 'medium' | 'high';
  description: string;
  location: {
    chapter: number;
    paragraph?: number;
  };
  suggestion: string;
}

// 任务处理类型
export interface ProcessingTask {
  id: string;
  type: 'NOVEL_ANALYSIS' | 'CHAPTER_ANALYSIS' | 'CHAPTER_MODIFY' | 'CONSISTENCY_CHECK';
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  progress: number;
  input?: any;
  output?: any;
  error?: string;
  novelId?: string;
  chapterId?: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

// 文件处理类型
export interface FileUploadResult {
  filename: string;
  originalName: string;
  size: number;
  mimetype: string;
  path: string;
}

// 错误类型
export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}
