"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChapterService = void 0;
const database_1 = __importDefault(require("../config/database"));
const aiService_1 = require("./aiService");
const errorHandler_1 = require("../middleware/errorHandler");
class ChapterService {
    constructor() {
        this.aiService = new aiService_1.AIService();
    }
    async getChapters(novelId, page = 1, limit = 20) {
        const skip = (page - 1) * limit;
        const [chapters, total] = await Promise.all([
            database_1.default.chapter.findMany({
                where: { novelId },
                include: {
                    chapterCharacters: {
                        include: {
                            character: {
                                select: {
                                    id: true,
                                    name: true,
                                    importanceScore: true
                                }
                            }
                        }
                    }
                },
                orderBy: { number: 'asc' },
                skip,
                take: limit
            }),
            database_1.default.chapter.count({
                where: { novelId }
            })
        ]);
        return {
            chapters: chapters.map(chapter => ({
                ...chapter,
                characters: chapter.chapterCharacters.map(cc => cc.character)
            })),
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }
    async getChapterById(id) {
        const chapter = await database_1.default.chapter.findUnique({
            where: { id },
            include: {
                novel: {
                    select: {
                        id: true,
                        title: true,
                        author: true
                    }
                },
                chapterCharacters: {
                    include: {
                        character: true
                    }
                }
            }
        });
        if (!chapter) {
            return null;
        }
        return {
            ...chapter,
            characters: chapter.chapterCharacters.map(cc => ({
                ...cc.character,
                aliases: cc.character.aliases ? JSON.parse(cc.character.aliases) : [],
                personality: cc.character.personality ? JSON.parse(cc.character.personality) : [],
                importance: cc.importance
            }))
        };
    }
    async modifyChapter(chapterId, rules) {
        const chapter = await database_1.default.chapter.findUnique({
            where: { id: chapterId },
            include: {
                novel: {
                    include: {
                        characters: true,
                        plotLines: true
                    }
                }
            }
        });
        if (!chapter) {
            throw (0, errorHandler_1.createError)('Chapter not found', 404);
        }
        // 更新章节状态为修改中
        await database_1.default.chapter.update({
            where: { id: chapterId },
            data: { status: 'MODIFYING' }
        });
        try {
            // 构建上下文信息
            const context = await this.buildContext(chapter);
            // 使用AI重写章节
            const rewriteResult = await this.aiService.rewriteText(chapter.originalContent, rules, context);
            // 保存修改结果
            const updatedChapter = await database_1.default.chapter.update({
                where: { id: chapterId },
                data: {
                    modifiedContent: rewriteResult.rewrittenText,
                    status: 'MODIFIED',
                    modifiedAt: new Date()
                }
            });
            return {
                chapter: updatedChapter,
                rewriteResult,
                context
            };
        }
        catch (error) {
            // 更新状态为错误
            await database_1.default.chapter.update({
                where: { id: chapterId },
                data: { status: 'ERROR' }
            });
            throw error;
        }
    }
    async batchModifyChapters(chapterIds, rules) {
        const results = [];
        for (const chapterId of chapterIds) {
            try {
                const result = await this.modifyChapter(chapterId, rules);
                results.push({
                    chapterId,
                    success: true,
                    result
                });
            }
            catch (error) {
                results.push({
                    chapterId,
                    success: false,
                    error: error.message
                });
            }
        }
        return {
            results,
            successCount: results.filter(r => r.success).length,
            failureCount: results.filter(r => !r.success).length
        };
    }
    async getChapterHistory(chapterId) {
        const chapter = await database_1.default.chapter.findUnique({
            where: { id: chapterId },
            select: {
                id: true,
                number: true,
                title: true,
                originalContent: true,
                modifiedContent: true,
                status: true,
                modifiedAt: true,
                createdAt: true
            }
        });
        if (!chapter) {
            throw (0, errorHandler_1.createError)('Chapter not found', 404);
        }
        return {
            chapter,
            versions: [
                {
                    version: 'original',
                    content: chapter.originalContent,
                    timestamp: chapter.createdAt,
                    description: '原始版本'
                },
                ...(chapter.modifiedContent ? [{
                        version: 'modified',
                        content: chapter.modifiedContent,
                        timestamp: chapter.modifiedAt,
                        description: '修改版本'
                    }] : [])
            ]
        };
    }
    async restoreChapter(chapterId) {
        const chapter = await database_1.default.chapter.update({
            where: { id: chapterId },
            data: {
                modifiedContent: null,
                status: 'ORIGINAL',
                modifiedAt: null
            }
        });
        return {
            chapter,
            message: 'Chapter restored to original version'
        };
    }
    async buildContext(chapter) {
        // 获取前一章的摘要
        const previousChapter = await database_1.default.chapter.findFirst({
            where: {
                novelId: chapter.novelId,
                number: chapter.number - 1
            },
            select: {
                title: true,
                originalContent: true,
                analysis: true
            }
        });
        // 获取下一章的预览
        const nextChapter = await database_1.default.chapter.findFirst({
            where: {
                novelId: chapter.novelId,
                number: chapter.number + 1
            },
            select: {
                title: true,
                analysis: true
            }
        });
        // 获取相关角色
        const chapterCharacters = await database_1.default.chapterCharacter.findMany({
            where: { chapterId: chapter.id },
            include: {
                character: true
            }
        });
        // 获取相关情节线
        const relevantPlotLines = chapter.novel.plotLines.filter(plot => {
            const chapters = plot.chapters ? JSON.parse(plot.chapters) : [];
            return chapters.includes(chapter.number);
        });
        return {
            previousChapter: previousChapter ? {
                title: previousChapter.title,
                summary: previousChapter.analysis?.summary ||
                    previousChapter.originalContent.substring(0, 200) + '...'
            } : null,
            nextChapter: nextChapter ? {
                title: nextChapter.title,
                preview: nextChapter.analysis?.summary || null
            } : null,
            characters: chapterCharacters.map(cc => ({
                name: cc.character.name,
                aliases: cc.character.aliases ? JSON.parse(cc.character.aliases) : [],
                personality: cc.character.personality ? JSON.parse(cc.character.personality) : [],
                importance: cc.importance
            })),
            plotLines: relevantPlotLines.map(plot => ({
                name: plot.name,
                type: plot.type,
                description: plot.description
            })),
            novelContext: {
                title: chapter.novel.title,
                author: chapter.novel.author,
                totalChapters: chapter.novel.totalChapters
            }
        };
    }
    async analyzeChapter(chapterId) {
        const chapter = await database_1.default.chapter.findUnique({
            where: { id: chapterId },
            include: {
                novel: {
                    include: {
                        characters: true
                    }
                }
            }
        });
        if (!chapter) {
            throw (0, errorHandler_1.createError)('Chapter not found', 404);
        }
        // 更新状态为分析中
        await database_1.default.chapter.update({
            where: { id: chapterId },
            data: { status: 'ANALYZING' }
        });
        try {
            // 使用AI分析章节
            const analysis = await this.aiService.analyzeText(chapter.originalContent, 'chapter');
            // 提取章节中的角色
            const characters = await this.aiService.extractCharacters(chapter.originalContent);
            // 分析情节
            const plotAnalysis = await this.aiService.analyzePlot(chapter.originalContent);
            // 生成摘要
            const summary = await this.aiService.generateSummary(chapter.originalContent, 'medium');
            const chapterAnalysis = {
                summary: summary.summary,
                characters: characters.characters || [],
                plot: plotAnalysis,
                contentType: analysis.contentType || {},
                emotionalTone: analysis.emotionalTone || 'neutral',
                pacing: analysis.pacing || 'medium',
                importanceScore: analysis.importanceScore || 50,
                themes: analysis.themes || [],
                keyEvents: analysis.keyEvents || []
            };
            // 保存分析结果
            const updatedChapter = await database_1.default.chapter.update({
                where: { id: chapterId },
                data: {
                    analysis: chapterAnalysis,
                    status: 'ANALYZED'
                }
            });
            // 更新角色-章节关联
            for (const char of characters.characters || []) {
                const character = chapter.novel.characters.find(c => c.name === char.name);
                if (character) {
                    await database_1.default.chapterCharacter.upsert({
                        where: {
                            chapterId_characterId: {
                                chapterId: chapter.id,
                                characterId: character.id
                            }
                        },
                        create: {
                            chapterId: chapter.id,
                            characterId: character.id,
                            importance: char.importance || 50
                        },
                        update: {
                            importance: char.importance || 50
                        }
                    });
                }
            }
            return {
                chapter: updatedChapter,
                analysis: chapterAnalysis
            };
        }
        catch (error) {
            // 更新状态为错误
            await database_1.default.chapter.update({
                where: { id: chapterId },
                data: { status: 'ERROR' }
            });
            throw error;
        }
    }
}
exports.ChapterService = ChapterService;
//# sourceMappingURL=chapterService.js.map