import { GoogleGenerativeAI } from '@google/generative-ai';
declare const genAI: GoogleGenerativeAI;
export declare const getGeminiModel: (modelName?: string) => import("@google/generative-ai").GenerativeModel;
export declare const defaultModelConfig: {
    temperature: number;
    topK: number;
    topP: number;
    maxOutputTokens: number;
};
export declare const textGenerationConfig: {
    temperature: number;
    topK: number;
    topP: number;
    maxOutputTokens: number;
};
export declare const analysisConfig: {
    temperature: number;
    topK: number;
    topP: number;
    maxOutputTokens: number;
};
export default genAI;
//# sourceMappingURL=gemini.d.ts.map