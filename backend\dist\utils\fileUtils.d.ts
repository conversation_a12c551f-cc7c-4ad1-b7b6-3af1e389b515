export declare class FileUtils {
    static ensureDirectoryExists(dirPath: string): Promise<void>;
    static readTextFile(filePath: string): Promise<string>;
    static writeTextFile(filePath: string, content: string): Promise<void>;
    static deleteFile(filePath: string): Promise<void>;
    static copyFile(sourcePath: string, destPath: string): Promise<void>;
    static getFileStats(filePath: string): Promise<{
        size: number;
        created: Date;
        modified: Date;
        isFile: boolean;
        isDirectory: boolean;
    }>;
    static getFileExtension(filename: string): string;
    static getBaseName(filename: string): string;
    static isTextFile(filename: string): boolean;
    static generateUniqueFilename(originalName: string): string;
    static createBackup(filePath: string): Promise<string>;
}
//# sourceMappingURL=fileUtils.d.ts.map