export declare class TextUtils {
    /**
     * 统计文本字数（中文按字符计算，英文按单词计算）
     */
    static countWords(text: string): number;
    /**
     * 提取文本摘要（取前N个字符）
     */
    static extractSummary(text: string, maxLength?: number): string;
    /**
     * 检测章节标题
     */
    static isChapterTitle(line: string): boolean;
    /**
     * 提取章节号
     */
    static extractChapterNumber(title: string): number | null;
    /**
     * 清理文本（移除多余空白、特殊字符等）
     */
    static cleanText(text: string): string;
    /**
     * 分割文本为段落
     */
    static splitIntoParagraphs(text: string): string[];
    /**
     * 检测对话内容
     */
    static isDialogue(text: string): boolean;
    /**
     * 提取对话内容
     */
    static extractDialogue(text: string): string;
    /**
     * 检测动作描述
     */
    static isActionDescription(text: string): boolean;
    /**
     * 检测环境描述
     */
    static isEnvironmentDescription(text: string): boolean;
    /**
     * 计算文本相似度（简单的字符匹配）
     */
    static calculateSimilarity(text1: string, text2: string): number;
    /**
     * 生成文本指纹（用于去重）
     */
    static generateFingerprint(text: string): string;
    /**
     * 格式化文本用于显示
     */
    static formatForDisplay(text: string, maxLength?: number): string;
}
//# sourceMappingURL=textUtils.d.ts.map