export class TextUtils {
  /**
   * 统计文本字数（中文按字符计算，英文按单词计算）
   */
  static countWords(text: string): number {
    if (!text) return 0;
    
    // 移除多余的空白字符
    const cleanText = text.trim().replace(/\s+/g, ' ');
    
    // 分离中文和英文
    const chineseChars = cleanText.match(/[\u4e00-\u9fa5]/g) || [];
    const englishWords = cleanText.replace(/[\u4e00-\u9fa5]/g, '').match(/\b\w+\b/g) || [];
    
    return chineseChars.length + englishWords.length;
  }

  /**
   * 提取文本摘要（取前N个字符）
   */
  static extractSummary(text: string, maxLength: number = 200): string {
    if (!text) return '';
    
    const cleanText = text.trim().replace(/\s+/g, ' ');
    if (cleanText.length <= maxLength) return cleanText;
    
    return cleanText.substring(0, maxLength) + '...';
  }

  /**
   * 检测章节标题
   */
  static isChapterTitle(line: string): boolean {
    const trimmed = line.trim();
    
    // 匹配 "第X章" 或 "第X回" 格式
    const chapterPattern = /^第[一二三四五六七八九十\d]+[章回]/;
    
    return chapterPattern.test(trimmed);
  }

  /**
   * 提取章节号
   */
  static extractChapterNumber(title: string): number | null {
    const match = title.match(/第([一二三四五六七八九十\d]+)[章回]/);
    if (!match) return null;
    
    const numberStr = match[1];
    
    // 转换中文数字
    const chineseNumbers = {
      '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
      '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
    };
    
    if (chineseNumbers[numberStr]) {
      return chineseNumbers[numberStr];
    }
    
    // 处理十几、几十的情况
    if (numberStr.includes('十')) {
      if (numberStr === '十') return 10;
      if (numberStr.startsWith('十')) {
        const unit = chineseNumbers[numberStr[1]];
        return unit ? 10 + unit : null;
      }
      if (numberStr.endsWith('十')) {
        const tens = chineseNumbers[numberStr[0]];
        return tens ? tens * 10 : null;
      }
      // 几十几的情况
      const parts = numberStr.split('十');
      if (parts.length === 2) {
        const tens = chineseNumbers[parts[0]] || 0;
        const units = chineseNumbers[parts[1]] || 0;
        return tens * 10 + units;
      }
    }
    
    // 阿拉伯数字
    const arabicNumber = parseInt(numberStr);
    return isNaN(arabicNumber) ? null : arabicNumber;
  }

  /**
   * 清理文本（移除多余空白、特殊字符等）
   */
  static cleanText(text: string): string {
    if (!text) return '';
    
    return text
      .replace(/\r\n/g, '\n')  // 统一换行符
      .replace(/\r/g, '\n')    // 统一换行符
      .replace(/\n{3,}/g, '\n\n')  // 合并多个换行
      .replace(/[ \t]+/g, ' ')  // 合并多个空格
      .trim();
  }

  /**
   * 分割文本为段落
   */
  static splitIntoParagraphs(text: string): string[] {
    if (!text) return [];
    
    return text
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
  }

  /**
   * 检测对话内容
   */
  static isDialogue(text: string): boolean {
    const trimmed = text.trim();
    
    // 检测中文引号
    if (trimmed.startsWith('"') && trimmed.endsWith('"')) return true;
    if (trimmed.startsWith('"') && trimmed.endsWith('"')) return true;
    if (trimmed.startsWith('「') && trimmed.endsWith('」')) return true;
    
    // 检测英文引号
    if (trimmed.startsWith('"') && trimmed.endsWith('"')) return true;
    if (trimmed.startsWith("'") && trimmed.endsWith("'")) return true;
    
    return false;
  }

  /**
   * 提取对话内容
   */
  static extractDialogue(text: string): string {
    const trimmed = text.trim();
    
    // 移除引号
    if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
      return trimmed.slice(1, -1);
    }
    if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
      return trimmed.slice(1, -1);
    }
    if (trimmed.startsWith('「') && trimmed.endsWith('」')) {
      return trimmed.slice(1, -1);
    }
    if (trimmed.startsWith('"') && trimmed.endsWith('"')) {
      return trimmed.slice(1, -1);
    }
    if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
      return trimmed.slice(1, -1);
    }
    
    return trimmed;
  }

  /**
   * 检测动作描述
   */
  static isActionDescription(text: string): boolean {
    const actionKeywords = [
      '走', '跑', '站', '坐', '躺', '跳', '飞', '游',
      '看', '听', '说', '笑', '哭', '喊', '叫',
      '拿', '放', '抓', '推', '拉', '打', '踢',
      '想', '思考', '回忆', '决定', '选择'
    ];
    
    return actionKeywords.some(keyword => text.includes(keyword));
  }

  /**
   * 检测环境描述
   */
  static isEnvironmentDescription(text: string): boolean {
    const envKeywords = [
      '天空', '云', '太阳', '月亮', '星星',
      '山', '水', '河', '湖', '海', '树', '花', '草',
      '房子', '建筑', '街道', '城市', '村庄',
      '风', '雨', '雪', '雷', '闪电'
    ];
    
    return envKeywords.some(keyword => text.includes(keyword));
  }

  /**
   * 计算文本相似度（简单的字符匹配）
   */
  static calculateSimilarity(text1: string, text2: string): number {
    if (!text1 || !text2) return 0;
    
    const len1 = text1.length;
    const len2 = text2.length;
    const maxLen = Math.max(len1, len2);
    
    if (maxLen === 0) return 1;
    
    let matches = 0;
    const minLen = Math.min(len1, len2);
    
    for (let i = 0; i < minLen; i++) {
      if (text1[i] === text2[i]) {
        matches++;
      }
    }
    
    return matches / maxLen;
  }

  /**
   * 生成文本指纹（用于去重）
   */
  static generateFingerprint(text: string): string {
    if (!text) return '';
    
    // 简单的哈希算法
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * 格式化文本用于显示
   */
  static formatForDisplay(text: string, maxLength?: number): string {
    if (!text) return '';
    
    let formatted = this.cleanText(text);
    
    if (maxLength && formatted.length > maxLength) {
      formatted = formatted.substring(0, maxLength) + '...';
    }
    
    return formatted;
  }
}
