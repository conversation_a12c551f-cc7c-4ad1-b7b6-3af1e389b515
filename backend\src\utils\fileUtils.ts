import fs from 'fs/promises';
import path from 'path';
import { createError } from '../middleware/errorHandler';

export class FileUtils {
  static async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.access(dirPath);
    } catch {
      await fs.mkdir(dirPath, { recursive: true });
    }
  }

  static async readTextFile(filePath: string): Promise<string> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      return content;
    } catch (error) {
      throw createError(`Failed to read file: ${error.message}`, 500);
    }
  }

  static async writeTextFile(filePath: string, content: string): Promise<void> {
    try {
      const dir = path.dirname(filePath);
      await this.ensureDirectoryExists(dir);
      await fs.writeFile(filePath, content, 'utf-8');
    } catch (error) {
      throw createError(`Failed to write file: ${error.message}`, 500);
    }
  }

  static async deleteFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      // 文件不存在时不抛出错误
      if (error.code !== 'ENOENT') {
        throw createError(`Failed to delete file: ${error.message}`, 500);
      }
    }
  }

  static async copyFile(sourcePath: string, destPath: string): Promise<void> {
    try {
      const dir = path.dirname(destPath);
      await this.ensureDirectoryExists(dir);
      await fs.copyFile(sourcePath, destPath);
    } catch (error) {
      throw createError(`Failed to copy file: ${error.message}`, 500);
    }
  }

  static async getFileStats(filePath: string) {
    try {
      const stats = await fs.stat(filePath);
      return {
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime,
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory()
      };
    } catch (error) {
      throw createError(`Failed to get file stats: ${error.message}`, 500);
    }
  }

  static getFileExtension(filename: string): string {
    return path.extname(filename).toLowerCase();
  }

  static getBaseName(filename: string): string {
    return path.basename(filename, path.extname(filename));
  }

  static isTextFile(filename: string): boolean {
    const textExtensions = ['.txt', '.md', '.json', '.csv', '.log'];
    return textExtensions.includes(this.getFileExtension(filename));
  }

  static generateUniqueFilename(originalName: string): string {
    const timestamp = Date.now();
    const random = Math.round(Math.random() * 1E9);
    const ext = this.getFileExtension(originalName);
    const base = this.getBaseName(originalName);
    return `${base}-${timestamp}-${random}${ext}`;
  }

  static async createBackup(filePath: string): Promise<string> {
    const backupPath = `${filePath}.backup.${Date.now()}`;
    await this.copyFile(filePath, backupPath);
    return backupPath;
  }
}
