"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const errorHandler_1 = require("../middleware/errorHandler");
const novelService_1 = require("../services/novelService");
const router = (0, express_1.Router)();
const novelService = new novelService_1.NovelService();
// 配置文件上传
const storage = multer_1.default.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path_1.default.extname(file.originalname));
    }
});
const upload = (0, multer_1.default)({
    storage,
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'text/plain' || path_1.default.extname(file.originalname) === '.txt') {
            cb(null, true);
        }
        else {
            cb(new Error('Only .txt files are allowed'));
        }
    },
    limits: {
        fileSize: 50 * 1024 * 1024 // 50MB
    }
});
// 上传并导入小说
router.post('/upload', upload.single('novel'), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.file) {
        return res.status(400).json({
            success: false,
            error: 'No file uploaded'
        });
    }
    const { projectId, title, author } = req.body;
    if (!projectId) {
        return res.status(400).json({
            success: false,
            error: 'Project ID is required'
        });
    }
    const result = await novelService.importNovel({
        projectId,
        title: title || path_1.default.basename(req.file.originalname, '.txt'),
        author,
        filePath: req.file.path,
        originalName: req.file.originalname
    });
    res.status(201).json({
        success: true,
        data: result
    });
}));
// 获取小说列表
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { projectId } = req.query;
    const novels = await novelService.getNovels(projectId);
    res.json({
        success: true,
        data: novels
    });
}));
// 获取单个小说详情
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const novel = await novelService.getNovelById(id);
    if (!novel) {
        return res.status(404).json({
            success: false,
            error: 'Novel not found'
        });
    }
    res.json({
        success: true,
        data: novel
    });
}));
// 分析小说结构
router.post('/:id/analyze', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const result = await novelService.analyzeNovel(id);
    res.json({
        success: true,
        data: result
    });
}));
// 获取小说分析结果
router.get('/:id/analysis', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const analysis = await novelService.getNovelAnalysis(id);
    res.json({
        success: true,
        data: analysis
    });
}));
exports.default = router;
//# sourceMappingURL=novels.js.map