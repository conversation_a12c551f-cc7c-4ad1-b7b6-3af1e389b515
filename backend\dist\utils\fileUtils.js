"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUtils = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const errorHandler_1 = require("../middleware/errorHandler");
class FileUtils {
    static async ensureDirectoryExists(dirPath) {
        try {
            await promises_1.default.access(dirPath);
        }
        catch {
            await promises_1.default.mkdir(dirPath, { recursive: true });
        }
    }
    static async readTextFile(filePath) {
        try {
            const content = await promises_1.default.readFile(filePath, 'utf-8');
            return content;
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Failed to read file: ${error.message}`, 500);
        }
    }
    static async writeTextFile(filePath, content) {
        try {
            const dir = path_1.default.dirname(filePath);
            await this.ensureDirectoryExists(dir);
            await promises_1.default.writeFile(filePath, content, 'utf-8');
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Failed to write file: ${error.message}`, 500);
        }
    }
    static async deleteFile(filePath) {
        try {
            await promises_1.default.unlink(filePath);
        }
        catch (error) {
            // 文件不存在时不抛出错误
            if (error.code !== 'ENOENT') {
                throw (0, errorHandler_1.createError)(`Failed to delete file: ${error.message}`, 500);
            }
        }
    }
    static async copyFile(sourcePath, destPath) {
        try {
            const dir = path_1.default.dirname(destPath);
            await this.ensureDirectoryExists(dir);
            await promises_1.default.copyFile(sourcePath, destPath);
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Failed to copy file: ${error.message}`, 500);
        }
    }
    static async getFileStats(filePath) {
        try {
            const stats = await promises_1.default.stat(filePath);
            return {
                size: stats.size,
                created: stats.birthtime,
                modified: stats.mtime,
                isFile: stats.isFile(),
                isDirectory: stats.isDirectory()
            };
        }
        catch (error) {
            throw (0, errorHandler_1.createError)(`Failed to get file stats: ${error.message}`, 500);
        }
    }
    static getFileExtension(filename) {
        return path_1.default.extname(filename).toLowerCase();
    }
    static getBaseName(filename) {
        return path_1.default.basename(filename, path_1.default.extname(filename));
    }
    static isTextFile(filename) {
        const textExtensions = ['.txt', '.md', '.json', '.csv', '.log'];
        return textExtensions.includes(this.getFileExtension(filename));
    }
    static generateUniqueFilename(originalName) {
        const timestamp = Date.now();
        const random = Math.round(Math.random() * 1E9);
        const ext = this.getFileExtension(originalName);
        const base = this.getBaseName(originalName);
        return `${base}-${timestamp}-${random}${ext}`;
    }
    static async createBackup(filePath) {
        const backupPath = `${filePath}.backup.${Date.now()}`;
        await this.copyFile(filePath, backupPath);
        return backupPath;
    }
}
exports.FileUtils = FileUtils;
//# sourceMappingURL=fileUtils.js.map