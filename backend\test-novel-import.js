const fs = require('fs');
const path = require('path');

// 测试小说解析功能
function parseChapters(content) {
  const lines = content.split('\n');
  const chapters = [];
  let currentChapter = null;

  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // 检测章节标题 (第X章 或 第X回)
    const chapterMatch = trimmedLine.match(/^第[一二三四五六七八九十\d]+[章回]\s*(.*)$/);
    
    if (chapterMatch) {
      // 保存上一章节
      if (currentChapter && currentChapter.content.trim()) {
        chapters.push(currentChapter);
      }
      
      // 开始新章节
      currentChapter = {
        title: trimmedLine,
        content: ''
      };
    } else if (currentChapter) {
      // 添加内容到当前章节
      currentChapter.content += line + '\n';
    }
  }

  // 添加最后一章
  if (currentChapter && currentChapter.content.trim()) {
    chapters.push(currentChapter);
  }

  return chapters;
}

// 测试函数
async function testNovelParsing() {
  try {
    console.log('🔍 开始测试小说解析功能...');
    
    // 读取小说文件
    const novelPath = path.join(__dirname, '..', '《神国之上》.txt');
    console.log(`📖 读取小说文件: ${novelPath}`);
    
    const content = fs.readFileSync(novelPath, 'utf-8');
    console.log(`📊 文件大小: ${content.length} 字符`);
    
    // 解析章节
    const chapters = parseChapters(content);
    console.log(`📚 解析到 ${chapters.length} 个章节`);
    
    // 显示前5章的信息
    console.log('\n📋 前5章信息:');
    chapters.slice(0, 5).forEach((chapter, index) => {
      console.log(`${index + 1}. ${chapter.title}`);
      console.log(`   字数: ${chapter.content.length}`);
      console.log(`   预览: ${chapter.content.substring(0, 100).replace(/\n/g, ' ')}...`);
      console.log('');
    });
    
    // 统计信息
    const totalWords = chapters.reduce((sum, chapter) => sum + chapter.content.length, 0);
    const avgWordsPerChapter = Math.round(totalWords / chapters.length);
    
    console.log('📈 统计信息:');
    console.log(`   总章节数: ${chapters.length}`);
    console.log(`   总字数: ${totalWords}`);
    console.log(`   平均每章字数: ${avgWordsPerChapter}`);
    console.log(`   最长章节: ${Math.max(...chapters.map(c => c.content.length))} 字`);
    console.log(`   最短章节: ${Math.min(...chapters.map(c => c.content.length))} 字`);
    
    console.log('\n✅ 小说解析测试完成!');
    
    return {
      success: true,
      chapters,
      stats: {
        totalChapters: chapters.length,
        totalWords,
        avgWordsPerChapter
      }
    };
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// 运行测试
if (require.main === module) {
  testNovelParsing().then(result => {
    if (result.success) {
      console.log('\n🎉 测试成功完成!');
    } else {
      console.log('\n💥 测试失败!');
    }
  });
}

module.exports = { testNovelParsing, parseChapters };
