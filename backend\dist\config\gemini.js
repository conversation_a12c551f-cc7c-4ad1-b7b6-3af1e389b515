"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.analysisConfig = exports.textGenerationConfig = exports.defaultModelConfig = exports.getGeminiModel = void 0;
const generative_ai_1 = require("@google/generative-ai");
if (!process.env.GEMINI_API_KEY) {
    throw new Error('GEMINI_API_KEY is required');
}
// 初始化 Gemini AI
const genAI = new generative_ai_1.GoogleGenerativeAI(process.env.GEMINI_API_KEY);
// 获取模型实例
const getGeminiModel = (modelName = 'gemini-2.0-flash-exp') => {
    return genAI.getGenerativeModel({ model: modelName });
};
exports.getGeminiModel = getGeminiModel;
// 默认模型配置
exports.defaultModelConfig = {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 8192,
};
// 文本生成配置
exports.textGenerationConfig = {
    temperature: 0.8,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 4096,
};
// 分析配置
exports.analysisConfig = {
    temperature: 0.3,
    topK: 20,
    topP: 0.8,
    maxOutputTokens: 2048,
};
exports.default = genAI;
//# sourceMappingURL=gemini.js.map