export interface ImportNovelParams {
    projectId: string;
    title: string;
    author?: string;
    filePath: string;
    originalName: string;
}
export declare class NovelService {
    private aiService;
    constructor();
    importNovel(params: ImportNovelParams): Promise<{
        novel: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            author: string | null;
            summary: string | null;
            originalPath: string;
            totalChapters: number;
            totalWords: number;
            status: import(".prisma/client").$Enums.NovelStatus;
            projectId: string;
        };
        chaptersCount: number;
        totalWords: number;
    }>;
    private parseChapters;
    getNovels(projectId?: string): Promise<({
        project: {
            id: string;
            name: string;
        };
        _count: {
            chapters: number;
            characters: number;
            plotLines: number;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        author: string | null;
        summary: string | null;
        originalPath: string;
        totalChapters: number;
        totalWords: number;
        status: import(".prisma/client").$Enums.NovelStatus;
        projectId: string;
    })[]>;
    getNovelById(id: string): Promise<{
        project: {
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            settings: import("@prisma/client/runtime/library").JsonValue | null;
        };
        _count: {
            chapters: number;
            characters: number;
            plotLines: number;
        };
        chapters: {
            number: number;
            id: string;
            title: string;
            status: import(".prisma/client").$Enums.ChapterStatus;
            wordCount: number;
            modifiedAt: Date;
        }[];
        characters: {
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            novelId: string;
            importanceScore: number;
            aliases: string | null;
            personality: string | null;
            background: string | null;
            firstAppearance: number | null;
            characterArc: string | null;
        }[];
        plotLines: {
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            status: import(".prisma/client").$Enums.PlotStatus;
            chapters: string;
            novelId: string;
            type: import(".prisma/client").$Enums.PlotType;
            keyEvents: import("@prisma/client/runtime/library").JsonValue | null;
        }[];
        knowledgeBase: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            novelId: string;
            worldBuilding: import("@prisma/client/runtime/library").JsonValue | null;
            themes: string | null;
            writingStyle: import("@prisma/client/runtime/library").JsonValue | null;
            vectorIndexPath: string | null;
            lastAnalyzed: Date | null;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        author: string | null;
        summary: string | null;
        originalPath: string;
        totalChapters: number;
        totalWords: number;
        status: import(".prisma/client").$Enums.NovelStatus;
        projectId: string;
    }>;
    analyzeNovel(novelId: string): Promise<{
        knowledgeBase: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            novelId: string;
            worldBuilding: import("@prisma/client/runtime/library").JsonValue | null;
            themes: string | null;
            writingStyle: import("@prisma/client/runtime/library").JsonValue | null;
            vectorIndexPath: string | null;
            lastAnalyzed: Date | null;
        };
        charactersCount: number;
        plotLinesCount: number;
        analysisTime: Date;
    }>;
    getNovelAnalysis(novelId: string): Promise<{
        novel: {
            id: string;
            title: string;
            author: string;
            status: import(".prisma/client").$Enums.NovelStatus;
            totalChapters: number;
            totalWords: number;
        };
        knowledgeBase: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            novelId: string;
            worldBuilding: import("@prisma/client/runtime/library").JsonValue | null;
            themes: string | null;
            writingStyle: import("@prisma/client/runtime/library").JsonValue | null;
            vectorIndexPath: string | null;
            lastAnalyzed: Date | null;
        };
        characters: {
            aliases: any;
            personality: any;
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            novelId: string;
            importanceScore: number;
            background: string | null;
            firstAppearance: number | null;
            characterArc: string | null;
        }[];
        plotLines: {
            chapters: any;
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            status: import(".prisma/client").$Enums.PlotStatus;
            novelId: string;
            type: import(".prisma/client").$Enums.PlotType;
            keyEvents: import("@prisma/client/runtime/library").JsonValue | null;
        }[];
    }>;
}
//# sourceMappingURL=novelService.d.ts.map