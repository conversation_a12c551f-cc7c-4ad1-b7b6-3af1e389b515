"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NovelService = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const database_1 = __importDefault(require("../config/database"));
const aiService_1 = require("./aiService");
const errorHandler_1 = require("../middleware/errorHandler");
class NovelService {
    constructor() {
        this.aiService = new aiService_1.AIService();
    }
    async importNovel(params) {
        const { projectId, title, author, filePath, originalName } = params;
        try {
            // 检查项目是否存在
            const project = await database_1.default.project.findUnique({
                where: { id: projectId }
            });
            if (!project) {
                throw (0, errorHandler_1.createError)('Project not found', 404);
            }
            // 读取文件内容
            const content = await promises_1.default.readFile(filePath, 'utf-8');
            // 分析文件结构
            const chapters = this.parseChapters(content);
            // 创建小说记录
            const novel = await database_1.default.novel.create({
                data: {
                    title,
                    author,
                    originalPath: filePath,
                    totalChapters: chapters.length,
                    totalWords: content.length,
                    projectId,
                    status: 'IMPORTED'
                }
            });
            // 批量创建章节
            await database_1.default.chapter.createMany({
                data: chapters.map((chapter, index) => ({
                    number: index + 1,
                    title: chapter.title,
                    originalContent: chapter.content,
                    wordCount: chapter.content.length,
                    novelId: novel.id,
                    status: 'ORIGINAL'
                }))
            });
            return {
                novel,
                chaptersCount: chapters.length,
                totalWords: content.length
            };
        }
        catch (error) {
            // 清理上传的文件
            try {
                await promises_1.default.unlink(filePath);
            }
            catch (unlinkError) {
                console.error('Failed to cleanup uploaded file:', unlinkError);
            }
            throw error;
        }
    }
    parseChapters(content) {
        const lines = content.split('\n');
        const chapters = [];
        let currentChapter = null;
        for (const line of lines) {
            const trimmedLine = line.trim();
            // 检测章节标题 (第X章 或 第X回)
            const chapterMatch = trimmedLine.match(/^第[一二三四五六七八九十\d]+[章回]\s*(.*)$/);
            if (chapterMatch) {
                // 保存上一章节
                if (currentChapter && currentChapter.content.trim()) {
                    chapters.push(currentChapter);
                }
                // 开始新章节
                currentChapter = {
                    title: trimmedLine,
                    content: ''
                };
            }
            else if (currentChapter) {
                // 添加内容到当前章节
                currentChapter.content += line + '\n';
            }
        }
        // 添加最后一章
        if (currentChapter && currentChapter.content.trim()) {
            chapters.push(currentChapter);
        }
        return chapters;
    }
    async getNovels(projectId) {
        const where = projectId ? { projectId } : {};
        return await database_1.default.novel.findMany({
            where,
            include: {
                project: {
                    select: {
                        id: true,
                        name: true
                    }
                },
                _count: {
                    select: {
                        chapters: true,
                        characters: true,
                        plotLines: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    }
    async getNovelById(id) {
        return await database_1.default.novel.findUnique({
            where: { id },
            include: {
                project: true,
                chapters: {
                    select: {
                        id: true,
                        number: true,
                        title: true,
                        status: true,
                        wordCount: true,
                        modifiedAt: true
                    },
                    orderBy: {
                        number: 'asc'
                    }
                },
                characters: {
                    orderBy: {
                        importanceScore: 'desc'
                    }
                },
                plotLines: {
                    orderBy: {
                        type: 'asc'
                    }
                },
                knowledgeBase: true,
                _count: {
                    select: {
                        chapters: true,
                        characters: true,
                        plotLines: true
                    }
                }
            }
        });
    }
    async analyzeNovel(novelId) {
        const novel = await database_1.default.novel.findUnique({
            where: { id: novelId },
            include: {
                chapters: {
                    orderBy: {
                        number: 'asc'
                    }
                }
            }
        });
        if (!novel) {
            throw (0, errorHandler_1.createError)('Novel not found', 404);
        }
        // 更新状态为分析中
        await database_1.default.novel.update({
            where: { id: novelId },
            data: { status: 'ANALYZING' }
        });
        try {
            // 合并所有章节内容进行整体分析
            const fullText = novel.chapters
                .map(chapter => `${chapter.title}\n${chapter.originalContent}`)
                .join('\n\n');
            // AI 分析小说结构
            const analysis = await this.aiService.analyzeNovelStructure(fullText);
            // 保存分析结果
            const knowledgeBase = await database_1.default.novelKnowledgeBase.upsert({
                where: { novelId },
                create: {
                    novelId,
                    worldBuilding: analysis.worldBuilding,
                    themes: JSON.stringify(analysis.themes),
                    writingStyle: analysis.writingStyle,
                    lastAnalyzed: new Date()
                },
                update: {
                    worldBuilding: analysis.worldBuilding,
                    themes: JSON.stringify(analysis.themes),
                    writingStyle: analysis.writingStyle,
                    lastAnalyzed: new Date()
                }
            });
            // 保存角色信息
            for (const character of analysis.characters) {
                await database_1.default.character.upsert({
                    where: {
                        novelId_name: {
                            novelId,
                            name: character.name
                        }
                    },
                    create: {
                        novelId,
                        name: character.name,
                        aliases: JSON.stringify(character.aliases),
                        description: character.description,
                        personality: JSON.stringify(character.personality),
                        background: character.background,
                        firstAppearance: character.firstAppearance,
                        importanceScore: character.importanceScore,
                        characterArc: character.characterArc
                    },
                    update: {
                        aliases: JSON.stringify(character.aliases),
                        description: character.description,
                        personality: JSON.stringify(character.personality),
                        background: character.background,
                        firstAppearance: character.firstAppearance,
                        importanceScore: character.importanceScore,
                        characterArc: character.characterArc
                    }
                });
            }
            // 保存情节线信息
            for (const plotLine of analysis.plotLines) {
                await database_1.default.plotLine.upsert({
                    where: {
                        novelId_name: {
                            novelId,
                            name: plotLine.name
                        }
                    },
                    create: {
                        novelId,
                        name: plotLine.name,
                        type: plotLine.type,
                        description: plotLine.description,
                        chapters: JSON.stringify(plotLine.chapters),
                        keyEvents: plotLine.keyEvents,
                        status: plotLine.status
                    },
                    update: {
                        type: plotLine.type,
                        description: plotLine.description,
                        chapters: JSON.stringify(plotLine.chapters),
                        keyEvents: plotLine.keyEvents,
                        status: plotLine.status
                    }
                });
            }
            // 更新小说状态
            await database_1.default.novel.update({
                where: { id: novelId },
                data: { status: 'ANALYZED' }
            });
            return {
                knowledgeBase,
                charactersCount: analysis.characters.length,
                plotLinesCount: analysis.plotLines.length,
                analysisTime: new Date()
            };
        }
        catch (error) {
            // 更新状态为错误
            await database_1.default.novel.update({
                where: { id: novelId },
                data: { status: 'ERROR' }
            });
            throw error;
        }
    }
    async getNovelAnalysis(novelId) {
        const novel = await database_1.default.novel.findUnique({
            where: { id: novelId },
            include: {
                knowledgeBase: true,
                characters: {
                    orderBy: {
                        importanceScore: 'desc'
                    }
                },
                plotLines: {
                    orderBy: {
                        type: 'asc'
                    }
                }
            }
        });
        if (!novel) {
            throw (0, errorHandler_1.createError)('Novel not found', 404);
        }
        return {
            novel: {
                id: novel.id,
                title: novel.title,
                author: novel.author,
                status: novel.status,
                totalChapters: novel.totalChapters,
                totalWords: novel.totalWords
            },
            knowledgeBase: novel.knowledgeBase,
            characters: novel.characters.map(char => ({
                ...char,
                aliases: char.aliases ? JSON.parse(char.aliases) : [],
                personality: char.personality ? JSON.parse(char.personality) : []
            })),
            plotLines: novel.plotLines.map(plot => ({
                ...plot,
                chapters: plot.chapters ? JSON.parse(plot.chapters) : []
            }))
        };
    }
}
exports.NovelService = NovelService;
//# sourceMappingURL=novelService.js.map