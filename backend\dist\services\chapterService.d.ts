export declare class ChapterService {
    private aiService;
    constructor();
    getChapters(novelId: string, page?: number, limit?: number): Promise<{
        chapters: {
            characters: {
                id: string;
                name: string;
                importanceScore: number;
            }[];
            chapterCharacters: ({
                character: {
                    id: string;
                    name: string;
                    importanceScore: number;
                };
            } & {
                id: string;
                importance: number;
                chapterId: string;
                characterId: string;
            })[];
            number: number;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            status: import(".prisma/client").$Enums.ChapterStatus;
            originalContent: string;
            modifiedContent: string | null;
            wordCount: number;
            analysis: import("@prisma/client/runtime/library").JsonValue | null;
            modifiedAt: Date | null;
            novelId: string;
        }[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getChapterById(id: string): Promise<{
        characters: {
            aliases: any;
            personality: any;
            importance: number;
            id: string;
            name: string;
            description: string | null;
            createdAt: Date;
            updatedAt: Date;
            novelId: string;
            importanceScore: number;
            background: string | null;
            firstAppearance: number | null;
            characterArc: string | null;
        }[];
        novel: {
            id: string;
            title: string;
            author: string;
        };
        chapterCharacters: ({
            character: {
                id: string;
                name: string;
                description: string | null;
                createdAt: Date;
                updatedAt: Date;
                novelId: string;
                importanceScore: number;
                aliases: string | null;
                personality: string | null;
                background: string | null;
                firstAppearance: number | null;
                characterArc: string | null;
            };
        } & {
            id: string;
            importance: number;
            chapterId: string;
            characterId: string;
        })[];
        number: number;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        title: string;
        status: import(".prisma/client").$Enums.ChapterStatus;
        originalContent: string;
        modifiedContent: string | null;
        wordCount: number;
        analysis: import("@prisma/client/runtime/library").JsonValue | null;
        modifiedAt: Date | null;
        novelId: string;
    }>;
    modifyChapter(chapterId: string, rules: any[]): Promise<{
        chapter: {
            number: number;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            status: import(".prisma/client").$Enums.ChapterStatus;
            originalContent: string;
            modifiedContent: string | null;
            wordCount: number;
            analysis: import("@prisma/client/runtime/library").JsonValue | null;
            modifiedAt: Date | null;
            novelId: string;
        };
        rewriteResult: {
            originalText: string;
            rewrittenText: any;
            rules: any[];
            timestamp: string;
        };
        context: {
            previousChapter: {
                title: string;
                summary: any;
            };
            nextChapter: {
                title: string;
                preview: any;
            };
            characters: {
                name: string;
                aliases: any;
                personality: any;
                importance: number;
            }[];
            plotLines: any;
            novelContext: {
                title: any;
                author: any;
                totalChapters: any;
            };
        };
    }>;
    batchModifyChapters(chapterIds: string[], rules: any[]): Promise<{
        results: any[];
        successCount: number;
        failureCount: number;
    }>;
    getChapterHistory(chapterId: string): Promise<{
        chapter: {
            number: number;
            id: string;
            createdAt: Date;
            title: string;
            status: import(".prisma/client").$Enums.ChapterStatus;
            originalContent: string;
            modifiedContent: string;
            modifiedAt: Date;
        };
        versions: {
            version: string;
            content: string;
            timestamp: Date;
            description: string;
        }[];
    }>;
    restoreChapter(chapterId: string): Promise<{
        chapter: {
            number: number;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            status: import(".prisma/client").$Enums.ChapterStatus;
            originalContent: string;
            modifiedContent: string | null;
            wordCount: number;
            analysis: import("@prisma/client/runtime/library").JsonValue | null;
            modifiedAt: Date | null;
            novelId: string;
        };
        message: string;
    }>;
    private buildContext;
    analyzeChapter(chapterId: string): Promise<{
        chapter: {
            number: number;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            title: string;
            status: import(".prisma/client").$Enums.ChapterStatus;
            originalContent: string;
            modifiedContent: string | null;
            wordCount: number;
            analysis: import("@prisma/client/runtime/library").JsonValue | null;
            modifiedAt: Date | null;
            novelId: string;
        };
        analysis: {
            summary: any;
            characters: any;
            plot: any;
            contentType: any;
            emotionalTone: any;
            pacing: any;
            importanceScore: any;
            themes: any;
            keyEvents: any;
        };
    }>;
}
//# sourceMappingURL=chapterService.d.ts.map