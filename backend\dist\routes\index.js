"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
// API 根路由
router.get('/', (req, res) => {
    res.json({
        message: '小说个性化重塑引擎 API',
        version: '1.0.0',
        endpoints: {
            projects: '/api/projects',
            novels: '/api/novels',
            chapters: '/api/chapters',
            ai: '/api/ai',
            health: '/health'
        },
        documentation: 'https://github.com/your-repo/novel-reshaper'
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map