"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const errorHandler_1 = require("../middleware/errorHandler");
const database_1 = __importDefault(require("../config/database"));
const router = (0, express_1.Router)();
// 获取所有项目
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const projects = await database_1.default.project.findMany({
        include: {
            novels: {
                select: {
                    id: true,
                    title: true,
                    status: true,
                    totalChapters: true,
                    createdAt: true
                }
            },
            _count: {
                select: {
                    novels: true,
                    modifyRules: true
                }
            }
        },
        orderBy: {
            updatedAt: 'desc'
        }
    });
    res.json({
        success: true,
        data: projects
    });
}));
// 获取单个项目
router.get('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const project = await database_1.default.project.findUnique({
        where: { id },
        include: {
            novels: {
                include: {
                    _count: {
                        select: {
                            chapters: true,
                            characters: true
                        }
                    }
                }
            },
            modifyRules: true
        }
    });
    if (!project) {
        return res.status(404).json({
            success: false,
            error: 'Project not found'
        });
    }
    res.json({
        success: true,
        data: project
    });
}));
// 创建新项目
router.post('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { name, description, settings } = req.body;
    if (!name) {
        return res.status(400).json({
            success: false,
            error: 'Project name is required'
        });
    }
    const project = await database_1.default.project.create({
        data: {
            name,
            description,
            settings: settings || {}
        }
    });
    res.status(201).json({
        success: true,
        data: project
    });
}));
// 更新项目
router.put('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const { name, description, settings } = req.body;
    const project = await database_1.default.project.update({
        where: { id },
        data: {
            name,
            description,
            settings
        }
    });
    res.json({
        success: true,
        data: project
    });
}));
// 删除项目
router.delete('/:id', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    await database_1.default.project.delete({
        where: { id }
    });
    res.json({
        success: true,
        message: 'Project deleted successfully'
    });
}));
exports.default = router;
//# sourceMappingURL=projects.js.map