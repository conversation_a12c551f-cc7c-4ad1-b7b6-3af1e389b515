{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "noImplicitAny": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowJs": true, "noEmit": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "ts-node": {"esm": false, "compilerOptions": {"module": "commonjs"}}}